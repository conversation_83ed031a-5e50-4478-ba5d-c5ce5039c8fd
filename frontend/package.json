{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lodash": "^4.17.21", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.30", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/estree": "^1.0.7", "@types/estree-jsx": "^1.0.5", "@types/hast": "^3.0.4", "@types/json-schema": "^7.0.15", "@types/lodash": "^4.17.17", "@types/mdast": "^4.0.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/unist": "^3.0.3", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}